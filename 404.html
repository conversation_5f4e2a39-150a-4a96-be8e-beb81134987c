<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found | ilikepancakes.ink >_<</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .error-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
            background: #fff;
            border: 2px solid #ccc;
            border-radius: 10px;
            box-shadow: 4px 4px 8px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .error-code {
            font-size: 4em;
            font-weight: bold;
            color: #9b59b6;
            text-shadow: 3px 3px #fff;
            margin: 0;
        }
        
        .error-message {
            font-size: 1.5em;
            color: #333;
            margin: 20px 0;
        }
        
        .pancake-stack {
            font-size: 3em;
            margin: 20px 0;
            animation: wobble 2s infinite ease-in-out;
        }
        
        @keyframes wobble {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-5deg); }
            75% { transform: rotate(5deg); }
        }
        
        .back-link {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(to bottom, #e0e0e0, #c0c0c0);
            border: 2px solid #999;
            border-radius: 5px;
            text-decoration: none;
            color: #000;
            font-weight: bold;
            margin-top: 20px;
            transition: all 0.2s ease;
        }
        
        .back-link:hover {
            background: linear-gradient(to bottom, #d0d0d0, #b0b0b0);
            transform: translateY(1px);
        }
    </style>
</head>
<body>
    <div class="background-animation" aria-hidden="true">
        <div class="floating-circles"></div>
        <div class="floating-text"></div>
    </div>
    
    <header role="banner">
        <div class="window-titlebar">
            <span class="window-title">~/pancakes/ilikepancakes.ink/404.html</span>
            <div class="window-controls">
                <div class="window-button">_</div>
                <div class="window-button">□</div>
                <div class="window-button close">×</div>
            </div>
        </div>
        <h1>ilikepancakes.ink >_<</h1>
        <nav role="navigation" aria-label="Main navigation">
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="qna.html">Q&A</a></li>
                <li><a href="linux/gnu-linux.html">Linux From Scratch</a></li>
                <li><a href="linux/arch-install-guide.html">Arch Install Guide</a></li>
                <li><a href="linux/archlinux.html">Why Arch Rules</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <div class="error-container">
            <div class="error-code">404</div>
            <div class="pancake-stack">🥞</div>
            <div class="error-message">Oops! This page got lost in the syrup! >_<</div>
            <p>The page you're looking for doesn't exist, but don't worry - there are still plenty of pancakes to go around! :3</p>
            <a href="/" class="back-link">🏠 Back to Homepage</a>
        </div>
    </main>
    
    <footer>
        <p>&copy; 2025 ilikepancakes.ink</p>
    </footer>
    
        <div class="visitor-counter">
            <p><strong>Visitor Counter:</strong></p>
            <img src="https://count.getloli.com/@tuffsite?name=tuffsite&theme=booru-lewd&padding=7&offset=0&align=top&scale=1&pixelated=1&darkmode=auto"
                 alt="Visitor counter showing site visits"
                 loading="lazy"
                 style="image-rendering: pixelated; image-rendering: -moz-crisp-edges; image-rendering: crisp-edges;">
        </div>

    <script src="script.js" defer></script>
</body>
</html>
