body {
    font-family: Arial, sans-serif;
    max-width: 700px;
    margin: 50px auto;
    padding: 20px;
    line-height: 1.6;
    color: #e0e0e0;
    background: #1a1a1a;
}

h1 {
    margin-bottom: 40px;
    text-align: center;
    font-size: 2.5em;
    color: #ffffff;
}

h2 {
    margin-bottom: 15px;
    color: #f0f0f0;
    border-bottom: 2px solid #333;
    padding-bottom: 5px;
}

section {
    margin-bottom: 40px;
    padding: 25px;
    background: #2a2a2a;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

p {
    margin-bottom: 15px;
    color: #cccccc;
}

.social-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.button {
    display: inline-block;
    padding: 12px 24px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.button.email {
    background: #007bff;
    color: white;
}

.button.email:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.button.github {
    background: #333;
    color: white;
}

.button.github:hover {
    background: #555;
    transform: translateY(-2px);
}

.button.twitter {
    background: #1da1f2;
    color: white;
}

.button.twitter:hover {
    background: #0d8bd9;
    transform: translateY(-2px);
}

.visitor-counter {
    margin-top: 30px;
    padding: 20px;
    background: #333;
    border-radius: 6px;
    text-align: center;
}

.visitor-counter p {
    margin-bottom: 10px;
    color: #ffffff;
}

.arch-button-container {
    text-align: center;
    margin-top: 30px;
    margin-bottom: 20px;
}

.button.arch {
    background: #1793d1;
    color: white;
    font-size: 1.1em;
    padding: 15px 30px;
}

.button.arch:hover {
    background: #0f7bb8;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    body {
        padding: 15px;
        margin: 20px auto;
    }

    h1 {
        font-size: 2em;
    }

    section {
        padding: 20px;
    }

    .social-buttons {
        flex-direction: column;
    }

    .button {
        text-align: center;
    }
}

pre {
    background-color: #282c34; /* Dark grey background */
    color: #abb2bf; /* Light grey text color */
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto; /* Enable horizontal scrolling for long lines */
    font-family: 'Fira Code', 'Cascadia Code', 'Consolas', monospace; /* Monospace font */
    font-size: 0.9em;
    line-height: 1.5;
}

code {
    font-family: 'Fira Code', 'Cascadia Code', 'Consolas', monospace;
    font-size: 0.9em;
}

/* Basic syntax highlighting (assuming classes are applied) */
.token.comment {
    color: #5c6370; /* Grey for comments */
}

.token.selector,
.token.keyword,
.token.tag {
    color: #e06c75; /* Reddish for keywords, tags, selectors */
}

.token.attr-name,
.token.property,
.token.boolean,
.token.number,
.token.constant,
.token.symbol {
    color: #d19a66; /* Orange for attribute names, properties, numbers, etc. */
}

.token.string,
.token.url {
    color: #98c379; /* Green for strings and URLs */
}

.token.punctuation {
    color: #abb2bf; /* Light grey for punctuation */
}

.token.attr-value {
    color: #98c379; /* Green for attribute values */
}

.token.function {
    color: #61afef; /* Blue for functions */
}
